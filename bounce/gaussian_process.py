import logging
from typing import Optional

import gin
import gpytorch
import numpy as np
import torch
from botorch import fit_gpytorch_mll
from botorch.exceptions import ModelFittingError
from botorch.models import SingleTaskGP
from gpytorch import ExactMarginalLogLikelihood
from gpytorch.kernels import ScaleKernel
from gpytorch.likelihoods import GaussianLikelihood
from torch import Tensor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, WhiteKernel
from sklearn.preprocessing import StandardScaler

from bounce import settings
from bounce.kernel.categorical_mixture import MixtureKernel
from bounce.projection import AxUS
from bounce.util.benchmark import ParameterType


@gin.configurable
def get_gp(
        axus: AxUS,
        x: Tensor,
        fx: Tensor,
        lengthscale_prior_shape: float = 1.5,
        lengthscale_prior_rate: float = 0.1,
        outputscale_prior_shape: float = 1.5,
        outputscale_prior_rate: float = 0.5,
        noise_prior_shape: float = 1.1,
        noise_prior_rate: float = 0.05,
        lamda: Optional[float] = None,
        discrete_ard: bool = False,
        continuous_ard: bool = True,
) -> tuple[SingleTaskGP, Tensor, Tensor]:
    # 箭头是 类型提示，表示这个函数的返回值类型【一个单任务的高斯过程模型，归一化时用到的均值，归一化时用到的标准差】
    """
    Define the GP model.

    Args:
        axus: the AxUS object
        x: the input points
        fx: the function values at the input points
        lengthscale_prior_shape: the shape parameter of the lengthscale prior
        lengthscale_prior_rate: the rate parameter of the lengthscale prior
        outputscale_prior_shape: the shape parameter of the outputscale prior
        outputscale_prior_rate: the rate parameter of the outputscale prior
        noise_prior_shape: the shape parameter of the noise prior
        noise_prior_rate: the rate parameter of the noise prior
        lamda: the parameter for the weighted average in the mixturekernel. trainable if set to None
        discrete_ard: whether to use ARD for discrete parameters
        continuous_ard: whether to use ARD for continuous parameters

    Returns:
        the GP model, the input points, and the function values at the input points

    """

    assert not discrete_ard, "ARD for discrete parameters is not supported yet"
    assert continuous_ard, "ARD for continuous parameters is always used"

    continuous_dims = np.asarray(
        [i.item() for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)]
    )
    discrete_dims = np.setdiff1d(np.arange(axus.target_dim), continuous_dims)

    if len(discrete_dims) == 0:
        kernel = gpytorch.kernels.MaternKernel(
            nu=2.5,
            ard_num_dims=axus.target_dim,
            lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            # botorch 3,6
        )
    elif len(continuous_dims) == 0:
        kernel = gpytorch.kernels.MaternKernel(
            nu=2.5,
            ard_num_dims=None,
            lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            # botorch 3,6
        )
    else:
        kernel = MixtureKernel(
            discrete_dims=discrete_dims.tolist(),
            continuous_dims=continuous_dims.tolist(),
            discrete_lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            continuous_lengthscale_prior=gpytorch.priors.GammaPrior(
                lengthscale_prior_shape, lengthscale_prior_rate
            ),
            lamda=lamda,
        )

    covar_module = ScaleKernel(
        # Use the same lengthscale prior as in the TuRBO paper
        kernel,
        outputscale_prior=gpytorch.priors.GammaPrior(
            outputscale_prior_shape, outputscale_prior_rate
        ),
        # 1.5, 1, botorch: 2, 0.15
    )

    train_x = x.detach().clone()
    train_fx = fx[:, None].detach().clone()

    # Define the model
    likelihood = GaussianLikelihood(
        noise_prior=gpytorch.priors.GammaPrior(noise_prior_shape, noise_prior_rate)
    )

    model = SingleTaskGP(
        train_X=train_x,
        train_Y=train_fx,
        covar_module=covar_module,
        likelihood=likelihood,
    )
    return model, train_x, train_fx


def fit_mll(
        model: SingleTaskGP,
        train_x: Tensor,
        train_fx: Tensor,
        max_cholesky_size: int = 1000,
        use_scipy_lbfgs: bool = True,
) -> None:
    """
    Fit the GP model. If the LBFGS optimizer fails, use the Adam optimizer.

    Args:
        model: the GP model
        train_x: the input points
        train_fx: the function values at the input points
        max_cholesky_size: the maximum size of the Cholesky decomposition
         use_scipy_lbfgs: whether to use the scipy LBFGS optimizer, otherwise use the Adam optimizer

    Returns:
        None

    """
    # Set model to training mode
    model.train()
    model.likelihood.train()
    mll = ExactMarginalLogLikelihood(model.likelihood, model)
    with gpytorch.settings.max_cholesky_size(max_cholesky_size):
        lbgs_failed = False
        if use_scipy_lbfgs:
            try:
                fit_gpytorch_mll(
                    mll=mll,
                    model=model,
                    train_x=train_x,
                    train_fx=train_fx,
                )
                model.eval()
            except ModelFittingError:
                lbgs_failed = True

        if not use_scipy_lbfgs or lbgs_failed:
            if lbgs_failed:
                logging.warning(
                    "⚠ Failed to fit GP using LBFGS, using backup Adam optimizer"
                )
            optimizer = torch.optim.Adam([{"params": model.parameters()}], lr=0.1)

            for _ in range(settings.MLL_FITTING_ITERATIONS):
                optimizer.zero_grad()
                output = model(train_x)
                loss = -mll(output, train_fx.flatten())
                loss.backward()
                optimizer.step()

    model.eval()
    model.likelihood.eval()


class RBFSurrogateModel:
    """
    RBF代理模型，使用sklearn的GaussianProcessRegressor实现
    提供与BoTorch SingleTaskGP相似的接口
    """

    def __init__(self, train_x: Tensor, train_y: Tensor, length_scale: float = 1.0, noise_level: float = 1e-6):
        """
        初始化RBF代理模型

        Args:
            train_x: 训练输入数据 [n_samples, n_features]
            train_y: 训练目标数据 [n_samples, 1]
            length_scale: RBF核的长度尺度参数
            noise_level: 噪声水平
        """
        self.device = train_x.device
        self.dtype = train_x.dtype

        # 转换为numpy数组
        self.train_x_np = train_x.detach().cpu().numpy()
        self.train_y_np = train_y.detach().cpu().numpy().flatten()

        # 数据标准化
        self.x_scaler = StandardScaler()
        self.y_scaler = StandardScaler()

        self.train_x_scaled = self.x_scaler.fit_transform(self.train_x_np)
        self.train_y_scaled = self.y_scaler.fit_transform(self.train_y_np.reshape(-1, 1)).flatten()

        # 创建RBF核
        kernel = RBF(length_scale=length_scale, length_scale_bounds=(1e-3, 1e3)) + WhiteKernel(noise_level=noise_level)

        # 创建高斯过程回归器
        self.gpr = GaussianProcessRegressor(
            kernel=kernel,
            alpha=1e-6,  # 数值稳定性
            normalize_y=False,  # 我们已经手动标准化了
            n_restarts_optimizer=5,
            random_state=42
        )

        # 训练模型
        self.gpr.fit(self.train_x_scaled, self.train_y_scaled)

    def predict(self, x: Tensor, return_std: bool = True):
        """
        预测函数，模拟BoTorch模型的接口

        Args:
            x: 输入点 [n_points, n_features]
            return_std: 是否返回标准差

        Returns:
            如果return_std=True: (mean, std)
            如果return_std=False: mean
        """
        # 转换为numpy并标准化
        x_np = x.detach().cpu().numpy()
        x_scaled = self.x_scaler.transform(x_np)

        # 预测
        if return_std:
            mean_scaled, std_scaled = self.gpr.predict(x_scaled, return_std=True)

            # 反标准化
            mean = self.y_scaler.inverse_transform(mean_scaled.reshape(-1, 1)).flatten()
            # 标准差需要乘以y的标准差
            std = std_scaled * self.y_scaler.scale_[0]

            # 转换回tensor
            mean_tensor = torch.tensor(mean, dtype=self.dtype, device=self.device)
            std_tensor = torch.tensor(std, dtype=self.dtype, device=self.device)

            return mean_tensor, std_tensor
        else:
            mean_scaled = self.gpr.predict(x_scaled)
            mean = self.y_scaler.inverse_transform(mean_scaled.reshape(-1, 1)).flatten()
            mean_tensor = torch.tensor(mean, dtype=self.dtype, device=self.device)
            return mean_tensor

    def posterior(self, x: Tensor):
        """
        返回后验分布，模拟BoTorch的接口
        """
        mean, std = self.predict(x, return_std=True)

        class MockPosterior:
            def __init__(self, mean, variance):
                self.mean = mean.unsqueeze(-1)  # [n_points, 1]
                self.variance = variance.unsqueeze(-1) ** 2  # [n_points, 1]

        return MockPosterior(mean, std)


@gin.configurable
def get_rbf_model(
    x: Tensor,
    fx: Tensor,
    length_scale: float = 1.0,
    noise_level: float = 1e-6,
) -> RBFSurrogateModel:
    """
    创建RBF代理模型

    Args:
        x: 训练输入数据
        fx: 训练目标数据
        length_scale: RBF核的长度尺度
        noise_level: 噪声水平

    Returns:
        训练好的RBF代理模型
    """
    train_x = x.detach().clone()
    train_fx = fx[:, None].detach().clone() if fx.dim() == 1 else fx.detach().clone()

    model = RBFSurrogateModel(train_x, train_fx, length_scale, noise_level)
    return model


def generate_virtual_points(
    x_real: Tensor,
    fx_real: Tensor,
    axus: AxUS,
    n_virtual: int = 20,
    noise_std: float = 0.1,
    interpolation_weight: float = 0.3,
) -> tuple[Tensor, Tensor]:
    """
    生成虚拟训练点来增强代理模型训练

    策略：
    1. 在现有点附近添加噪声生成邻近点
    2. 在现有点之间进行插值
    3. 使用简单的函数关系估计虚拟点的函数值

    Args:
        x_real: 真实的训练输入点 [n_real, dim]
        fx_real: 真实的函数值 [n_real]
        axus: AxUS投影对象
        n_virtual: 要生成的虚拟点数量
        noise_std: 添加噪声的标准差
        interpolation_weight: 插值权重

    Returns:
        x_augmented: 增强后的输入点 [n_real + n_virtual, dim]
        fx_augmented: 增强后的函数值 [n_real + n_virtual]
    """
    if len(x_real) >= 10:  # 如果已有足够多的真实点，不需要虚拟点
        return x_real, fx_real

    n_real = len(x_real)
    device = x_real.device
    dtype = x_real.dtype

    virtual_x_list = []
    virtual_fx_list = []

    # 策略1: 在现有点附近添加噪声
    n_noise_points = min(n_virtual // 2, n_real * 3)
    for i in range(n_noise_points):
        # 随机选择一个真实点
        idx = torch.randint(0, n_real, (1,)).item()
        base_point = x_real[idx].clone()
        base_fx = fx_real[idx].item()

        # 添加噪声
        noise = torch.randn_like(base_point) * noise_std
        virtual_point = torch.clamp(base_point + noise, 0, 1)  # 确保在[0,1]范围内

        # 处理离散变量约束
        virtual_point = enforce_discrete_constraints(virtual_point, axus)

        # 估计虚拟点的函数值（添加小的随机扰动）
        virtual_fx = base_fx + torch.randn(1).item() * noise_std * torch.std(fx_real).item()

        virtual_x_list.append(virtual_point)
        virtual_fx_list.append(virtual_fx)

    # 策略2: 在现有点之间插值
    n_interp_points = n_virtual - len(virtual_x_list)
    for i in range(n_interp_points):
        if n_real >= 2:
            # 随机选择两个不同的真实点
            idx1, idx2 = torch.randperm(n_real)[:2]
            point1, point2 = x_real[idx1], x_real[idx2]
            fx1, fx2 = fx_real[idx1].item(), fx_real[idx2].item()

            # 随机插值权重
            alpha = torch.rand(1).item()
            virtual_point = alpha * point1 + (1 - alpha) * point2
            virtual_point = torch.clamp(virtual_point, 0, 1)

            # 处理离散变量约束
            virtual_point = enforce_discrete_constraints(virtual_point, axus)

            # 插值函数值
            virtual_fx = alpha * fx1 + (1 - alpha) * fx2
            # 添加一些不确定性
            virtual_fx += torch.randn(1).item() * noise_std * torch.std(fx_real).item() * 0.5

            virtual_x_list.append(virtual_point)
            virtual_fx_list.append(virtual_fx)
        else:
            # 如果只有一个真实点，在其附近随机生成
            base_point = x_real[0].clone()
            base_fx = fx_real[0].item()

            noise = torch.randn_like(base_point) * noise_std * 2
            virtual_point = torch.clamp(base_point + noise, 0, 1)
            virtual_point = enforce_discrete_constraints(virtual_point, axus)

            virtual_fx = base_fx + torch.randn(1).item() * noise_std * 2

            virtual_x_list.append(virtual_point)
            virtual_fx_list.append(virtual_fx)

    # 合并真实点和虚拟点
    if virtual_x_list:
        virtual_x = torch.stack(virtual_x_list)
        virtual_fx = torch.tensor(virtual_fx_list, dtype=dtype, device=device)

        x_augmented = torch.cat([x_real, virtual_x], dim=0)
        fx_augmented = torch.cat([fx_real, virtual_fx], dim=0)
    else:
        x_augmented = x_real
        fx_augmented = fx_real

    return x_augmented, fx_augmented


def enforce_discrete_constraints(x: Tensor, axus: AxUS) -> Tensor:
    """
    确保虚拟点满足离散变量约束

    Args:
        x: 输入点 [dim]
        axus: AxUS投影对象

    Returns:
        约束后的点
    """
    x_constrained = x.clone()

    # 处理每个bin的约束
    for bin_obj, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
        if len(indices) > 1:  # one-hot编码的分类变量
            # 找到最大值的位置，设为1，其他设为0
            max_idx = torch.argmax(x_constrained[indices])
            x_constrained[indices] = 0
            x_constrained[indices[max_idx]] = 1

    # 二分变量已经通过clamp确保在[0,1]范围内
    for bin_obj, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
        # 二分变量四舍五入到0或1
        x_constrained[indices] = torch.round(x_constrained[indices])

    return x_constrained
