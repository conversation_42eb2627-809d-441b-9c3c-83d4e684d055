import logging
import warnings
from typing import Optional, Union

import gin
import numpy as np
import torch
from botorch.acquisition import ExpectedImprovement, qExpectedImprovement
from botorch.models import SingleTaskGP
from botorch.optim import optimize_acqf
from botorch.sampling import SobolQMCNormalSampler
from gpytorch.kernels import MaternKernel

from bounce.kernel.categorical_mixture import MixtureKernel
from bounce.neighbors import hamming_distance, hamming_neighbors_within_tr
from bounce.projection import AxUS
from bounce.trust_region import TrustRegion
from bounce.util.benchmark import ParameterType
from bounce.gaussian_process import RBFSurrogateModel


@gin.configurable
def create_candidates_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: Union[SingleTaskGP, RBFSurrogateModel],
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int = 1,
    x_bests: Optional[list[torch.Tensor]] = None,
    add_spray_points: bool = True,
    sampler: Optional[SobolQMCNormalSampler] = None,
    # 遗传算法参数
    population_size: int = 100,
    n_generations: int = 50,
    crossover_rate: float = 0.8,
    mutation_rate: float = 0.1,
    elite_size: int = 5,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    Create candidate points using genetic algorithm for discrete variables.

    Args:
        model: The current GP or RBF model
        batch_size: The number of candidate points to create
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The approximate posterior samples
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        x_bests: The center of the trust region, should be in [0, 1]^d
        add_spray_points: Whether to add spray points (points within hamming distance 1 of the center)
        sampler: The sampler to use for the acquisition function
        population_size: Size of the genetic algorithm population
        n_generations: Number of generations to evolve
        crossover_rate: Probability of crossover
        mutation_rate: Probability of mutation
        elite_size: Number of elite individuals to preserve

    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    # 模型已经在bounce_ga.py中创建和训练完成，这里直接使用

    population_size = 5 * axus.target_dim

    # Get the indices of the continuous parameters
    indices_not_to_optimize = torch.tensor(
        [i for _, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)]
    )

    # Find the center of the trust region
    x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
    # x_center should be in [0, 1]^d at this point
    x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)
    if x_bests is not None:
        # replace
        x_centers[:, indices_not_to_optimize] = (
            x_bests[:, indices_not_to_optimize] + 1
        ) / 2

    x_batch_return = torch.zeros(
        (batch_size, axus.target_dim), dtype=x_scaled.dtype, device=x_scaled.device
    )
    fx_batch_return = torch.zeros(
        (batch_size, 1), dtype=fx_scaled.dtype, device=fx_scaled.device
    )

    # 获取不同类型变量的索引信息
    continuous_indices = []
    binary_indices = []
    categorical_groups = []  # 每个分类变量的one-hot索引组

    # 连续变量索引
    for _, indices in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
        continuous_indices.extend(indices.tolist())

    # 二分变量索引
    for _, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
        binary_indices.extend(indices.tolist())

    # 分类变量索引组（每个分类变量是一个one-hot编码组）
    for _, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
        categorical_groups.append(indices.tolist())

    continuous_indices = torch.tensor(continuous_indices, device=device) if continuous_indices else torch.tensor([], device=device, dtype=torch.long)
    binary_indices = torch.tensor(binary_indices, device=device) if binary_indices else torch.tensor([], device=device, dtype=torch.long)

    for batch_index in range(batch_size):
        _acquisition_function = acquisition_function
        if acquisition_function is None:
            # 对于RBF模型，不需要获取函数，直接使用UCB
            if isinstance(model, RBFSurrogateModel):
                _acquisition_function = None  # RBF模型在遗传算法中直接使用UCB
            else:
                assert (
                    sampler is not None
                ), "Either acquisition_function or sampler must be provided for GP models"
                x_pending = x_batch_return[:batch_index, :] if batch_index > 0 else None
                _acquisition_function = qExpectedImprovement(
                    model=model,
                    best_f=(-fx_scaled).max().item(),
                    sampler=sampler,
                    X_pending=x_pending,
                )

        def evaluate_fitness(x: torch.Tensor):
            """评估种群适应度（使用采集函数）"""
            if len(x.shape) == 1:
                x = x.unsqueeze(0)
            with torch.no_grad():
                if isinstance(model, RBFSurrogateModel):
                    # 对于RBF模型，直接使用预测均值作为适应度
                    mean, std = model.predict(x, return_std=True)
                    # 使用Upper Confidence Bound (UCB) 作为采集函数
                    ucb = mean + 2.0 * std  # 探索-利用平衡
                    return ucb.squeeze()
                else:
                    # 对于GP模型，使用原有的采集函数
                    return -_acquisition_function(x.unsqueeze(1)).squeeze()

        # 遗传算法优化
        x_best = run_genetic_algorithm_discrete(
            x_center=x_centers[batch_index],
            axus=axus,
            trust_region=trust_region,
            evaluate_fitness=evaluate_fitness,
            population_size=population_size,
            n_generations=n_generations,
            crossover_rate=crossover_rate,
            mutation_rate=mutation_rate,
            elite_size=elite_size,
            continuous_indices=continuous_indices,
            binary_indices=binary_indices,
            categorical_groups=categorical_groups,
            device=device,
            add_spray_points=add_spray_points,
        )

        # 评估最优解的适应度
        best_fitness = evaluate_fitness(x_best)

        # 存储结果
        x_batch_return[batch_index, :] = x_best.squeeze()
        fx_batch_return[batch_index, :] = best_fitness

    assert len(indices_not_to_optimize) == 0 or torch.any(
        x_centers[:, indices_not_to_optimize].squeeze()
        == x_batch_return[:, indices_not_to_optimize].squeeze()
    ), "x_ret should not be optimized at indices_not_to_optimize"

    # transform to [-1, 1], was [0, 1]
    x_batch_return = x_batch_return * 2 - 1

    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_discrete]),
    }

    return x_batch_return, fx_batch_return.reshape(batch_size), tr_state


def create_candidates_continuous(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int,
    indices_to_optimize: Optional[torch.Tensor] = None,
    x_bests: Optional[list[torch.Tensor]] = None,
    sampler: Optional[SobolQMCNormalSampler] = None,
    # 差分进化参数
    population_size: int = 100,
    n_generations: int = 5,
    F: float = 0.5,  # 差分权重
    CR: float = 0.7,  # 交叉概率
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    Create candidate points using differential evolution for continuous variables.

    Args:
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The acquisition function to use
        model: The current GP model
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        indices_to_optimize: The indices of the candidate points to optimize (in case of mixed spaces)
        x_bests: The center of the trust region
        batch_size: int
        population_size: Size of the differential evolution population
        n_generations: Number of generations to evolve
        F: Differential weight
        CR: Crossover probability

    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    if indices_to_optimize is None:
        indices_to_optimize = torch.arange(axus.target_dim)
    indices_not_to_optimize = torch.arange(axus.target_dim)[
        ~torch.isin(torch.arange(axus.target_dim), indices_to_optimize)
    ]

    x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
    # repeat x_centers batch_size many times
    x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)

    if x_bests is not None:
        x_centers[:, indices_not_to_optimize] = (
            x_bests[:, indices_not_to_optimize] + 1
        ) / 2

    assert len(x_centers.shape) == 2, "x_center should be a 2d tensor"

    fx_argmins = torch.zeros(batch_size, dtype=torch.long, device=device)
    x_cand_downs = torch.zeros(
        (batch_size, axus.target_dim), dtype=torch.double, device=device
    )
    for batch_index in range(batch_size):
        x_center = x_centers[batch_index, :]

        if isinstance(model.covar_module.base_kernel, MixtureKernel):
            weights = model.covar_module.base_kernel.continuous_kernel.lengthscale.detach().squeeze(
                0
            )
        elif isinstance(model.covar_module.base_kernel, MaternKernel):
            weights = model.covar_module.base_kernel.lengthscale.detach().squeeze(0)
        else:
            raise NotImplementedError(
                "Only MixtureKernel and MaternKernel are supported"
            )
        weights /= weights.mean()
        weights /= torch.prod(torch.pow(weights, 1 / len(weights)))
        _x_center = x_center[indices_to_optimize]
        _tr_lb = torch.clip(
            _x_center - trust_region.length_continuous * weights / 2, 0, 1
        )
        _tr_ub = torch.clip(
            _x_center + trust_region.length_continuous * weights / 2, 0, 1
        )
        tr_lb = torch.zeros(axus.target_dim, dtype=torch.double, device=device)
        tr_ub = torch.ones(axus.target_dim, dtype=torch.double, device=device)
        tr_lb[indices_to_optimize] = _tr_lb
        tr_ub[indices_to_optimize] = _tr_ub

        _acquisition_function = acquisition_function
        if acquisition_function is None:
            # 对于RBF模型，不需要获取函数，直接使用UCB
            if isinstance(model, RBFSurrogateModel):
                _acquisition_function = None  # RBF模型在遗传算法中直接使用UCB
            else:
                assert (
                    sampler is not None
                ), "Either acquisition_function or sampler must be provided for GP models"
                x_pending = x_cand_downs[:batch_index, :] if batch_index > 0 else None
                _acquisition_function = qExpectedImprovement(
                    model=model,
                    best_f=(-fx_scaled).max().item(),
                    sampler=sampler,
                    X_pending=x_pending,
                )

        def evaluate_fitness(x: torch.Tensor):
            """评估适应度（使用采集函数）"""
            if len(x.shape) == 1:
                x = x.unsqueeze(0)
            with torch.no_grad():
                if isinstance(model, RBFSurrogateModel):
                    # 对于RBF模型，直接使用预测均值作为适应度
                    mean, std = model.predict(x, return_std=True)
                    # 使用Upper Confidence Bound (UCB) 作为采集函数
                    ucb = mean + 2.0 * std  # 探索-利用平衡
                    return ucb.squeeze()
                else:
                    # 对于GP模型，使用原有的采集函数
                    return -_acquisition_function(x.unsqueeze(1)).squeeze()

        # 差分进化优化
        x_best = run_differential_evolution_continuous(
            x_center=x_center,
            tr_lb=tr_lb,
            tr_ub=tr_ub,
            indices_to_optimize=indices_to_optimize,
            indices_not_to_optimize=indices_not_to_optimize,
            evaluate_fitness=evaluate_fitness,
            population_size=population_size,
            n_generations=n_generations,
            F=F,
            CR=CR,
            device=device,
        )

        # 评估最优解的适应度
        best_fitness = evaluate_fitness(x_best)

        x_cand_downs[batch_index, :] = x_best
        fx_argmins[batch_index] = best_fitness

    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_continuous]),
        "lb": tr_lb.detach().cpu().numpy(),
        "ub": tr_ub.detach().cpu().numpy(),
    }
    
    return x_cand_downs * 2 - 1, fx_argmins.reshape(batch_size), tr_state


def sample_initial_points_discrete(
    x_center: torch.Tensor,
    tr_length: torch.Tensor,
    axus: AxUS,
    n_initial_points: int,
) -> torch.Tensor:
    """
    这是一个辅助函数，用于在离散搜索阶段随机采样一批候选点，作为后续采集函数打分的输入。

    Sample initial points for the discrete parameters

    Args:
        x_center: the center of the trust region
        tr_length: the length of the trust region
        axus: the AxUS embedding
        n_initial_points: the number of initial points to sample

    Returns:
        x_cand: the sampled initial points

    """

    # 获取所有离散参数类型（排除连续类型）
    discrete_parameter_types = [
        pt for pt in ParameterType if pt != ParameterType.CONTINUOUS
    ]

    # copy x_center n_initial_points times
    # # 复制中心点 n_initial_points 次；接下来会在这些复制的点上“有选择地修改某些维度”。
    x_cand = torch.repeat_interleave(x_center.unsqueeze(0), n_initial_points, dim=0)

    # 对不同类型的离散参数进行随机化
    for parameter_type in discrete_parameter_types:
        # 如果当前类型的bin数量为0，不需要优化
        if axus.n_bins_of_type(parameter_type) == 0:
            # No parameters of this type
            continue

        # 二进制参数
        if parameter_type == ParameterType.BINARY:
            # 拿到bin的索引
            indices = torch.tensor(
                [i for _, i in axus.bins_and_indices_of_type(parameter_type)]
            )
            # draw min(tr_length, len(indices)) indices for each candidate
            # 为每个候选点，随机选出 tr_length - 1 个 binary 维度
            indices_for_cand = torch.tensor(
                np.array(
                    [
                        np.random.choice(
                            indices, min(tr_length - 1, len(indices)), replace=False
                        )
                        for _ in range(n_initial_points)
                    ]
                ),
                dtype=torch.long,
                device=x_cand.device,
            )
            # draw values for each index
            # 为这些位置采样新的值（0 或 1）
            values_for_cand = torch.randint(
                0,
                2,
                (n_initial_points, len(indices_for_cand[0])),
                dtype=x_cand.dtype,
                device=x_cand.device,
            )
            # set values for each candidate
            # 把这些新值写入对应的候选点位置
            x_cand = x_cand.scatter_(1, indices_for_cand, values_for_cand)
        elif parameter_type == ParameterType.CATEGORICAL:
            # 获取所有分类参数的索引组;例如 indicess = [[3,4], [7,8,9], [12,13]]，表示有 3 个分类变量；
            indicess = [i for _, i in axus.bins_and_indices_of_type(parameter_type)]

            # # 如果分类变量组数量超过信任域长度，随机选择部分 min(tr_length, len(indicess) 组
            if len(indicess) > tr_length:
                # index_setss的形式：[[1,2], [0,2], [1,2]]
                index_setss = [
                    np.random.choice(
                        np.arange(len(indicess)),
                        min(tr_length, len(indicess)),
                        replace=False,
                    )
                    # 选择n_initial_points次
                    for _ in range(n_initial_points)
                ]
                # 拿到索引以及对应索引组 例如i=1,index_sets=[0,2],这里的0,2又对应indicess中的[3,4],[12,13]
                for i, index_sets in enumerate(index_setss):
                    # 这里就是替换index_sets=[0,2]为[[3,4],[12,13]]
                    index_sets = [indicess[i] for i in index_sets]
                    # set x_cand to 0 for each index
                    # 涉及的one-hot变量全部变为0
                    x_cand[i, torch.cat(index_sets)] = 0

                    # 随机选择一位为1
                    if True:  # else:
                        # this is the expensive part
                        for indices in index_sets:
                            # set one index to 1
                            x_cand[i, np.random.choice(indices)] = 1
            else:
                # 处理和之前类似，所有涉及的fenlie
                for indices in indicess:
                    # set x_cand to 0 for each index
                    x_cand[:, indices] = 0
                    # sample n_initial_points indices
                    indices_for_cand = np.random.choice(indices, n_initial_points)
                    # set one index to 1
                    x_cand[torch.arange(n_initial_points), indices_for_cand] = 1
            pass

        elif parameter_type == ParameterType.ORDINAL:
            raise NotImplementedError("Ordinal parameters are not supported yet")
        else:
            raise ValueError(f"Unknown parameter type {parameter_type}")

    # remove duplicates
    # 移除重复点，移动到和最优点邻居合并之后
    x_cand = torch.unique(x_cand, dim=0)

    # remove points that coincide with x_center
    # 移除与中心点相同的点
    x_cand = x_cand[torch.any(x_cand != x_center, dim=1), :]

    # remove candidates that are not within the trust region
    # 移除不在信任域内的点
    x_cand_in_tr = x_cand[hamming_distance(x_cand, x_center) <= tr_length, :]
    if len(x_cand_in_tr) == 0:
        logging.debug(f"No initial points in trust region, returning all candidates")
        
    # 如果信任域内有点，返回这些点；否则返回所有候选点，
    # 就是说如果点都不在TR内，可以适当放宽条件，保证算法运行
    return x_cand_in_tr if len(x_cand_in_tr) > 0 else x_cand


def run_genetic_algorithm_discrete(
    x_center: torch.Tensor,
    axus: AxUS,
    trust_region: TrustRegion,
    evaluate_fitness,
    population_size: int,
    n_generations: int,
    crossover_rate: float,
    mutation_rate: float,
    elite_size: int,
    continuous_indices: torch.Tensor,  # 保留但不使用，为了接口一致性
    binary_indices: torch.Tensor,
    categorical_groups: list,
    device: str,
    add_spray_points: bool = True,
) -> torch.Tensor:
    """
    使用遗传算法优化离散变量

    Args:
        x_center: 信任域中心点
        axus: AxUS嵌入
        trust_region: 信任域
        evaluate_fitness: 适应度评估函数
        population_size: 种群大小
        n_generations: 进化代数
        crossover_rate: 交叉概率
        mutation_rate: 变异概率
        elite_size: 精英个体数量
        continuous_indices: 连续变量索引
        binary_indices: 二分变量索引
        categorical_groups: 分类变量索引组
        device: 设备
        add_spray_points: 是否添加喷射点

    Returns:
        最优个体
    """

    # 1. 种群初始化：结合随机采样和最优邻居
    population = x_center.unsqueeze(0)  # shape: (1, D)

    # 1.2 随机采样
    n_random = population_size // 2
    if n_random > 0:
        random_points = sample_initial_points_discrete(
            x_center=x_center,
            axus=axus,
            tr_length=trust_region.length_discrete,
            n_initial_points=n_random,
        )

        if isinstance(random_points, list):
            # 如果是 list of tensors
            if isinstance(random_points[0], torch.Tensor):
                random_points = torch.stack(random_points)
            else:
                random_points = torch.tensor(random_points, device=x_center.device, dtype=x_center.dtype)

        population = torch.vstack((population, random_points))

    # 1.3 添加喷射点（最优邻居）
    if add_spray_points:
        spray_points = hamming_neighbors_within_tr(
            x_center=x_center,
            x=x_center,
            tr_length=trust_region.length_discrete,
            axus=axus,
        )
        if isinstance(spray_points, list):
            spray_points = torch.stack([torch.tensor(p, device=x_center.device, dtype=x_center.dtype) for p in spray_points])
        population = torch.vstack((population, spray_points))

    # 1.4 填充剩余个体（如果当前种群数量不足）
    while len(population) < population_size:
        individual = x_center.clone()
        individual = mutate_individual(individual, mutation_rate, binary_indices, categorical_groups, device)
        if len(continuous_indices) > 0:
            individual[continuous_indices] = x_center[continuous_indices]
        population = torch.vstack((population, individual.unsqueeze(0)))

    # 截断到 population_size
    # population = population[:population_size]
    # population = torch.unique(population, dim=0)
    # 2. 进化循环
    for generation in range(n_generations):
        # 2.1 评估适应度（向量化）
        fitness = evaluate_fitness(population)  # ✅ 直接传入整个 population tensor

        # 自适应变异率和交叉率（可以固定也可以根据多样性调整）
        adaptive_crossover_rate = 0.7
        adaptive_mutation_rate = 0.3

        # 精英保留
        elite_indices = torch.topk(-fitness, k=elite_size, largest=True).indices
        elite = population[elite_indices]

        # 生成新个体
        new_population = [elite[i].clone() for i in range(elite_size)]

        # 通过锦标赛选择+交叉变异生成后代
        while len(new_population) < population_size:
            parent1 = tournament_selection(population, fitness, device)
            parent2 = tournament_selection(population, fitness, device)

            if torch.rand(1, device=device) < adaptive_crossover_rate:
                child = crossover_discrete(parent1, parent2, binary_indices, categorical_groups, device)
            else:
                child = parent1.clone()

            if torch.rand(1, device=device) < adaptive_mutation_rate:
                child = mutate_individual(child, adaptive_mutation_rate, binary_indices, categorical_groups, device)

            # 确保连续变量维度不变
            if len(continuous_indices) > 0:
                child[continuous_indices] = x_center[continuous_indices]

            new_population.append(child)

        # 更新种群为新的 tensor
        population = torch.stack(new_population)

        # 可选：多样性保护机制（略）

    # 3. 返回最优个体

    fitness = evaluate_fitness(population)
    best_idx = torch.argmin(fitness)
    best_individual = population[best_idx].clone()

    # 恢复连续变量
    if len(continuous_indices) > 0:
        best_individual[continuous_indices] = x_center[continuous_indices]

    return best_individual


def tournament_selection(population: torch.Tensor, fitness: torch.Tensor, device: str, k: int = 3) -> torch.Tensor:
    """锦标赛选择"""
    pop_size = len(population)
    indices = torch.randint(0, pop_size, (k,), device=device)
    tournament_fitness = fitness[indices]
    winner_idx = indices[torch.argmin(tournament_fitness)]  # 适应度越小越好
    return population[winner_idx]


def crossover_discrete(parent1: torch.Tensor, parent2: torch.Tensor,
                      binary_indices: torch.Tensor, categorical_groups: list, device: str) -> torch.Tensor:
    """考虑变量类型的交叉操作"""
    child = parent1.clone()

    # 二分变量：均匀交叉
    if len(binary_indices) > 0:
        crossover_mask = torch.rand(len(binary_indices), device=device) < 0.5
        child[binary_indices[crossover_mask]] = parent2[binary_indices[crossover_mask]]

    # 分类变量：整个one-hot组交叉
    for cat_indices in categorical_groups:
        if torch.rand(1, device=device).item() < 0.5:  # 50%概率交换整个分类变量
            cat_indices_tensor = torch.tensor(cat_indices, device=device)
            child[cat_indices_tensor] = parent2[cat_indices_tensor]

    return child


def mutate_individual(individual: torch.Tensor, mutation_rate: float,
                     binary_indices: torch.Tensor, categorical_groups: list, device: str) -> torch.Tensor:
    """考虑变量类型的变异操作"""
    mutated = individual.clone()

    # 二分变量变异：翻转
    if len(binary_indices) > 0 and torch.rand(1, device=device).item() < mutation_rate:
        mutation_indices = binary_indices[torch.rand(len(binary_indices), device=device) < 0.1]  # 10%的二分变量变异
        if len(mutation_indices) > 0:
            mutated[mutation_indices] = 1 - mutated[mutation_indices]  # 在[0,1]中翻转

    # 分类变量变异：重新随机选择类别
    for cat_indices in categorical_groups:
        if torch.rand(1, device=device).item() < mutation_rate * 0.5:  # 降低分类变量变异率
            cat_indices_tensor = torch.tensor(cat_indices, device=device)
            # 清零当前one-hot
            mutated[cat_indices_tensor] = 0  # 在[0,1]编码中，0表示未选中
            # 随机选择一个新类别
            selected_idx = torch.randint(0, len(cat_indices), (1,), device=device).item()
            mutated[cat_indices_tensor[selected_idx]] = 1  # 1表示选中

    return mutated


def ensure_categorical_constraints_simple(individual: torch.Tensor, categorical_groups: list, device: str) -> torch.Tensor:
    """确保分类变量满足one-hot约束"""
    corrected = individual.clone()

    for cat_indices in categorical_groups:
        cat_indices_tensor = torch.tensor(cat_indices, device=device)
        cat_values = corrected[cat_indices_tensor]

        # 检查是否有且仅有一个1
        positive_count = (cat_values > 0.5).sum()  # 在[0,1]范围内，大于0.5认为是1

        if positive_count != 1:
            # 违反约束，重新设置
            corrected[cat_indices_tensor] = 0  # 全部设为0
            # 随机选择一个位置设为1
            selected_idx = torch.randint(0, len(cat_indices), (1,), device=device).item()
            corrected[cat_indices_tensor[selected_idx]] = 1

    return corrected


def calculate_population_diversity(population: list, device: str) -> torch.Tensor:
    """
    计算种群多样性
    使用种群中个体间的平均欧氏距离作为多样性度量
    """
    if len(population) < 2:
        return torch.tensor(1.0, device=device)

    population_tensor = torch.stack(population)
    n_individuals = len(population)

    # 计算所有个体对之间的距离
    distances = []
    for i in range(n_individuals):
        for j in range(i + 1, n_individuals):
            dist = torch.norm(population_tensor[i] - population_tensor[j])
            distances.append(dist)

    if len(distances) == 0:
        return torch.tensor(0.0, device=device)

    # 返回平均距离，归一化到[0,1]范围
    avg_distance = torch.stack(distances).mean()
    # 假设最大可能距离是sqrt(维度数)，进行归一化
    max_possible_distance = torch.sqrt(torch.tensor(population_tensor.shape[1], dtype=torch.float, device=device))
    normalized_diversity = torch.clamp(avg_distance / max_possible_distance, 0.0, 1.0)

    return normalized_diversity


def run_differential_evolution_continuous(
    x_center: torch.Tensor,
    tr_lb: torch.Tensor,
    tr_ub: torch.Tensor,
    indices_to_optimize: torch.Tensor,
    indices_not_to_optimize: torch.Tensor,  # 保留但不使用，为了接口一致性
    evaluate_fitness,
    population_size: int,
    n_generations: int,
    F: float,
    CR: float,
    device: str,
) -> torch.Tensor:
    """
    使用差分进化优化连续变量

    Args:
        x_center: 信任域中心点
        tr_lb: 信任域下界
        tr_ub: 信任域上界
        indices_to_optimize: 需要优化的维度索引
        indices_not_to_optimize: 不需要优化的维度索引
        evaluate_fitness: 适应度评估函数
        population_size: 种群大小
        n_generations: 进化代数
        F: 差分权重
        CR: 交叉概率
        device: 设备

    Returns:
        最优个体
    """

    # 1. 种群初始化
    population = []

    # 添加中心点
    population.append(x_center.clone())

    # 在信任域内随机初始化剩余个体
    for _ in range(population_size - 1):
        individual = x_center.clone()
        # 只在需要优化的维度上随机化
        if len(indices_to_optimize) > 0:
            individual[indices_to_optimize] = torch.rand(len(indices_to_optimize), device=device) * (
                tr_ub[indices_to_optimize] - tr_lb[indices_to_optimize]
            ) + tr_lb[indices_to_optimize]
        population.append(individual)

    population = torch.stack(population)

    # 2. 进化循环
    for generation in range(n_generations):
        new_population = []

        # 计算种群多样性（用于自适应参数调整）
        population_list = [population[i] for i in range(population_size)]
        diversity = calculate_population_diversity(population_list, device)

        # 自适应调整变异强度和交叉率
        adaptive_F = F * (1.0 + 0.5 * torch.exp(-diversity * 10))  # 多样性低时增加变异强度
        adaptive_CR = min(0.9, CR * (1.0 + 0.3 * torch.exp(-diversity * 5)))  # 多样性低时增加交叉率

        for i in range(population_size):
            # 2.1 变异：DE/rand/1策略 + 多样性增强
            # 随机选择三个不同的个体
            candidates = list(range(population_size))
            candidates.remove(i)
            r1, r2, r3 = torch.randperm(len(candidates), device=device)[:3]
            r1, r2, r3 = candidates[r1], candidates[r2], candidates[r3]

            # 变异向量 - 使用自适应参数
            mutant = population[r1] + adaptive_F * (population[r2] - population[r3])

            # 添加额外的随机扰动以增加多样性
            if diversity < 0.1:  # 当多样性很低时
                noise = torch.randn_like(mutant) * 0.05 * (tr_ub - tr_lb)
                mutant = mutant + noise

            # 确保在信任域内
            mutant = torch.clamp(mutant, tr_lb, tr_ub)

            # 2.2 交叉 - 使用自适应交叉率
            trial = population[i].clone()
            crossover_mask = torch.rand(len(trial), device=device) < adaptive_CR

            # 确保至少有一个维度被交叉
            if not crossover_mask.any():
                random_idx = torch.randint(0, len(indices_to_optimize), (1,), device=device)
                crossover_mask[indices_to_optimize[random_idx]] = True

            # 只在需要优化的维度上进行交叉
            for idx in indices_to_optimize:
                if crossover_mask[idx]:
                    trial[idx] = mutant[idx]

            # 2.3 选择 - 改进的选择策略
            trial_fitness = evaluate_fitness(trial)
            current_fitness = evaluate_fitness(population[i])

            # 基本贪婪选择 + 多样性保护
            if trial_fitness < current_fitness:
                new_population.append(trial)
            else:
                # 以小概率接受较差解以保持多样性
                acceptance_prob = 0.05 * torch.exp(-diversity * 20)  # 多样性低时增加接受概率
                if torch.rand(1, device=device) < acceptance_prob:
                    new_population.append(trial)
                else:
                    new_population.append(population[i])

        population = torch.stack(new_population)

    # 3. 返回最优个体
    fitness_values = torch.zeros(population_size, device=device)
    for i in range(population_size):
        fitness_values[i] = evaluate_fitness(population[i])

    best_idx = torch.argmin(fitness_values)
    return population[best_idx]
