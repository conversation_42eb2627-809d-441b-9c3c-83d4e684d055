import torch
from typing import <PERSON><PERSON>

from bounce.projection import AxUS
from bounce.util.benchmark import ParameterType
from bounce.trust_region import TrustRegion


def hamming_distance(
    x: torch.Tensor,
    y: torch.Tensor,
    axus: AxUS = None,
) -> torch.Tensor:
    """
    Compute the Hamming distance between a set of points (x) and a vector (y)
    考虑不同变量类型的正确距离计算方式：
    - 二分变量：每个维度直接比较
    - 分类变量：使用one-hot编码，只有当1的位置不同时才算距离为1
    - 连续变量：不参与汉明距离计算

    Args:
        x: The set of points
        y: The second vector
        axus: The AxUS embedding for handling different variable types

    Returns:
        The Hamming distance between the points and the vector
    """
    if len(x.shape) == 1:
        x = x.unsqueeze(0)
    assert len(x.shape) == 2, "x must be a matrix"
    if len(y.shape) == 2:
        y = y.squeeze()
    assert len(y.shape) == 1, "y must be a vector"

    if axus is None:
        # 如果没有提供axus，使用原来的简单方法
        return torch.sum(x != y, dim=1)

    # 使用改进的距离计算方法
    distances = torch.zeros(x.shape[0], device=x.device, dtype=torch.float)

    # 处理二分变量
    if axus.n_bins_of_type(ParameterType.BINARY) > 0:
        binary_indices = torch.cat([
            indices for _, indices in axus.bins_and_indices_of_type(ParameterType.BINARY)
        ])
        if len(binary_indices) > 0:
            binary_distances = torch.sum(x[:, binary_indices] != y[binary_indices], dim=1)
            distances += binary_distances.float()

    # 处理分类变量
    if axus.n_bins_of_type(ParameterType.CATEGORICAL) > 0:
        for _, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
            # 对于每个分类变量，检查1的位置是否相同
            x_cat = x[:, indices]  # [batch_size, n_categories]
            y_cat = y[indices]     # [n_categories]

            # 找到y中1的位置
            y_active = torch.where(y_cat == 1)[0]
            if len(y_active) > 0:
                y_active_idx = y_active[0]
                # 检查x中对应位置是否也是1
                x_active_match = x_cat[:, y_active_idx] == 1
                # 如果不匹配，距离+1
                distances += (~x_active_match).float()

    # 连续变量不参与汉明距离计算

    return distances


def hamming_neighbors_within_tr(
    x: torch.Tensor,
    x_center: torch.Tensor,
    tr_length: torch.Tensor,
    axus: AxUS,
) -> torch.Tensor:
    """
    Find the neighbors of the points in x that are within Hamming distance 1 and still the trust region

    Args:
        x: The points to compute the neighbors for
        x_center: The center of the trust region
        tr_length: The length of the trust region
        axus: The AxUS embedding

    Returns:
        The neighbors of the points in x that are within Hamming distance 1 and still the trust region
    """
    x = torch.clone(x)
    if len(x.shape) == 2:
        x = x.squeeze()
    assert len(x.shape) == 1, "x must be a vector"

    discrete_parameter_types = [
        pt for pt in ParameterType if pt != ParameterType.CONTINUOUS
    ]

    neighbors_for_type = dict()

    for parameter_type in discrete_parameter_types:
        if axus.n_bins_of_type(parameter_type) == 0:
            # No parameters of this type
            continue
        if parameter_type == ParameterType.BINARY:
            indices = torch.tensor(
                [i for _, i in axus.bins_and_indices_of_type(parameter_type)]
            )
            diagonal = torch.zeros_like(x)
            diagonal[indices] = 1
            diag_nonzero = diagonal != 0

            type_neighbors = torch.abs(torch.diag(diagonal) - x.unsqueeze(0))[
                diag_nonzero, :
            ]
        elif parameter_type == ParameterType.CATEGORICAL:
            indicess = [i for _, i in axus.bins_and_indices_of_type(parameter_type)]
            type_neighbors = torch.zeros((0, len(x)), device=x.device)
            for indices in indicess:
                # find inactive indices
                inactive_indices = [i for i in indices if x[i] == 0]
                # create len(inactive_index) copies of x
                x_copies = torch.repeat_interleave(
                    x.unsqueeze(0), len(inactive_indices), dim=0
                )
                x_copies[:, indices] = 0
                for i, inactive_index in enumerate(inactive_indices):
                    x_copies[i, inactive_index] = 1
                # vstack x_copies to type_neighbors
                type_neighbors = torch.vstack((type_neighbors, x_copies))
        elif parameter_type == ParameterType.ORDINAL:
            raise NotImplementedError("Ordinal parameters are not supported yet")
        else:
            raise ValueError(f"Unknown parameter type {parameter_type}")

        # add type_neighbors to neighbors_for_type
        neighbors_for_type[parameter_type] = type_neighbors

    # stack all neighbors
    neighbors = torch.vstack(
        [type_neighbors for type_neighbors in neighbors_for_type.values()]
    )
    # remove duplicates
    neighbors = torch.unique(neighbors, dim=0)
    # remove the original point
    neighbors = neighbors[torch.any(neighbors != x, dim=1), :]
    # remove the neighbors that are not within the trust region
    neighbors = neighbors[hamming_distance(neighbors, x_center, axus) <= tr_length, :]
    return neighbors


def get_local_tr_points(
        x_center: torch.Tensor,
        trust_region: TrustRegion,
        bounce,  # 避免循环导入
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
        获取以当前最优点为中心，在信赖域范围内的所有已评估过的点
        用于训练局部代理模型

        Args:
            x_center: 当前最优点（信赖域中心）
            trust_region: 信赖域对象
            bounce: Bounce算法实例

        Returns:
            tuple: (在信赖域范围内的点, 对应的函数值)
    """
    # 从全局已评估的点中筛选在信赖域内的点
    if len(bounce.x_global) == 0:
        # 如果没有已评估的点，返回空张量
        empty_points = torch.empty(0, bounce.random_embedding.target_dim,
                                  dtype=bounce.dtype, device=bounce.device)
        empty_values = torch.empty(0, dtype=bounce.dtype, device=bounce.device)
        return empty_points, empty_values

    # 计算所有全局点到中心点的汉明距离
    distances = hamming_distance(
        x=bounce.x_global,
        y=x_center,
        axus=bounce.random_embedding
    )

    # 筛选在信赖域内的点
    within_tr_mask = distances <= trust_region.length_discrete
    local_tr_points = bounce.x_global[within_tr_mask]
    local_tr_values = bounce.fx_global[within_tr_mask]

    return local_tr_points, local_tr_values