# RBF代理模型配置文件
# 使用RBF模型替代传统GP模型，并启用虚拟点增强

# 导入默认配置
include 'bounce_ga/default.gin'

# 覆盖代理模型配置 - 使用RBF模型 + 虚拟点
Bounce.use_rbf_model = True
Bounce.use_virtual_points = True
Bounce.n_virtual_points = 20

# 遗传算法参数
create_candidates_discrete.population_size = 100
create_candidates_discrete.n_generations = 20

# 优化器配置 (覆盖默认值)
Bounce.maximum_number_evaluations = 50  # 减少评估次数用于快速测试
Bounce.device = "cpu"  # 使用CPU而不是CUDA
