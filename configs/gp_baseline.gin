# 传统GP模型配置文件
# 使用传统的高斯过程模型作为基线

# 导入默认配置
include 'bounce_ga/default.gin'

# 代理模型配置 - 使用GP基线模型 (不使用虚拟点)
Bounce.use_rbf_model = False
Bounce.use_virtual_points = False
Bounce.n_virtual_points = 20

# 遗传算法参数
create_candidates_discrete.population_size = 100
create_candidates_discrete.n_generations = 20

# 优化器配置 (覆盖默认值)
Bounce.maximum_number_evaluations = 100  # 增加评估次数进行更充分的测试
Bounce.maximum_number_evaluations_until_input_dim = 100  # 确保不超过总评估次数
Bounce.device = "cpu"  # 使用CPU而不是CUDA
