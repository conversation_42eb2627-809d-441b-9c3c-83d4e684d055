#!/usr/bin/env python3
"""
RBF代理模型功能演示脚本
展示RBF模型和虚拟点生成的功能
"""

import torch
import logging
import gin
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def demo_rbf_vs_gp():
    """演示RBF模型与GP模型的对比"""
    
    logging.info("🔬 开始RBF模型与GP模型对比演示")
    
    try:
        from bounce.gaussian_process import RBFSurrogateModel, get_rbf_model, get_gp
        
        # 创建测试数据 - 模拟一个简单的函数
        np.random.seed(42)
        torch.manual_seed(42)
        
        # 训练数据
        n_train = 8
        x_train = torch.rand(n_train, 5)  # 5维输入
        # 创建一个有趣的测试函数：Ackley函数的简化版本
        fx_train = torch.zeros(n_train)
        for i in range(n_train):
            x = x_train[i] * 10 - 5  # 缩放到[-5, 5]
            fx_train[i] = -20 * torch.exp(-0.2 * torch.sqrt(torch.mean(x**2))) - torch.exp(torch.mean(torch.cos(2*np.pi*x))) + 20 + np.e
        
        # 测试数据
        n_test = 20
        x_test = torch.rand(n_test, 5)
        fx_test_true = torch.zeros(n_test)
        for i in range(n_test):
            x = x_test[i] * 10 - 5
            fx_test_true[i] = -20 * torch.exp(-0.2 * torch.sqrt(torch.mean(x**2))) - torch.exp(torch.mean(torch.cos(2*np.pi*x))) + 20 + np.e
        
        logging.info(f"📊 训练数据: {n_train} 个点, 测试数据: {n_test} 个点")
        logging.info(f"训练函数值范围: [{fx_train.min():.3f}, {fx_train.max():.3f}]")
        
        # 1. 测试RBF模型
        logging.info("\n🤖 测试RBF模型...")
        rbf_model = get_rbf_model(x_train, fx_train)
        
        rbf_mean, rbf_std = rbf_model.predict(x_test, return_std=True)
        rbf_mae = torch.mean(torch.abs(rbf_mean - fx_test_true))
        rbf_rmse = torch.sqrt(torch.mean((rbf_mean - fx_test_true)**2))
        
        logging.info(f"RBF模型预测:")
        logging.info(f"  MAE: {rbf_mae:.4f}")
        logging.info(f"  RMSE: {rbf_rmse:.4f}")
        logging.info(f"  预测值范围: [{rbf_mean.min():.3f}, {rbf_mean.max():.3f}]")
        logging.info(f"  不确定性范围: [{rbf_std.min():.3f}, {rbf_std.max():.3f}]")
        
        # 2. 测试GP模型
        logging.info("\n🧠 测试GP模型...")
        try:
            gp_model = get_gp(x_train, fx_train)
            gp_posterior = gp_model.posterior(x_test)
            gp_mean = gp_posterior.mean.squeeze()
            gp_std = torch.sqrt(gp_posterior.variance.squeeze())
            
            gp_mae = torch.mean(torch.abs(gp_mean - fx_test_true))
            gp_rmse = torch.sqrt(torch.mean((gp_mean - fx_test_true)**2))
            
            logging.info(f"GP模型预测:")
            logging.info(f"  MAE: {gp_mae:.4f}")
            logging.info(f"  RMSE: {gp_rmse:.4f}")
            logging.info(f"  预测值范围: [{gp_mean.min():.3f}, {gp_mean.max():.3f}]")
            logging.info(f"  不确定性范围: [{gp_std.min():.3f}, {gp_std.max():.3f}]")
            
            # 对比结果
            logging.info(f"\n📈 模型对比:")
            logging.info(f"RBF vs GP MAE: {rbf_mae:.4f} vs {gp_mae:.4f} ({'RBF更好' if rbf_mae < gp_mae else 'GP更好'})")
            logging.info(f"RBF vs GP RMSE: {rbf_rmse:.4f} vs {gp_rmse:.4f} ({'RBF更好' if rbf_rmse < gp_rmse else 'GP更好'})")
            
        except Exception as e:
            logging.warning(f"⚠️ GP模型测试失败: {e}")
        
        # 3. 测试采集函数
        logging.info("\n🎯 测试采集函数...")
        
        # 使用UCB采集函数
        ucb_rbf = rbf_mean + 2.0 * rbf_std
        best_idx = torch.argmax(ucb_rbf)
        
        logging.info(f"RBF-UCB推荐的最佳点:")
        logging.info(f"  索引: {best_idx}")
        logging.info(f"  预测均值: {rbf_mean[best_idx]:.4f}")
        logging.info(f"  预测标准差: {rbf_std[best_idx]:.4f}")
        logging.info(f"  UCB值: {ucb_rbf[best_idx]:.4f}")
        logging.info(f"  真实值: {fx_test_true[best_idx]:.4f}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_virtual_points():
    """演示虚拟点生成功能"""
    
    logging.info("\n🔮 虚拟点生成功能演示")
    
    try:
        # 创建少量真实数据点
        n_real = 3
        x_real = torch.rand(n_real, 8)
        fx_real = torch.randn(n_real)
        
        logging.info(f"📊 真实数据点: {n_real} 个")
        logging.info(f"真实函数值: {fx_real}")
        
        # 由于虚拟点生成需要AxUS对象，我们创建一个简化版本
        logging.info("⚠️ 完整的虚拟点生成需要AxUS对象，这里展示基本概念")
        
        # 简化的虚拟点生成策略
        n_virtual = 10
        virtual_x_list = []
        virtual_fx_list = []
        
        # 策略1: 在现有点附近添加噪声
        for i in range(n_virtual // 2):
            base_idx = torch.randint(0, n_real, (1,)).item()
            base_point = x_real[base_idx].clone()
            base_fx = fx_real[base_idx].item()
            
            # 添加噪声
            noise = torch.randn_like(base_point) * 0.1
            virtual_point = torch.clamp(base_point + noise, 0, 1)
            virtual_fx = base_fx + torch.randn(1).item() * 0.1
            
            virtual_x_list.append(virtual_point)
            virtual_fx_list.append(virtual_fx)
        
        # 策略2: 在现有点之间插值
        for i in range(n_virtual - len(virtual_x_list)):
            if n_real >= 2:
                idx1, idx2 = torch.randperm(n_real)[:2]
                point1, point2 = x_real[idx1], x_real[idx2]
                fx1, fx2 = fx_real[idx1].item(), fx_real[idx2].item()
                
                alpha = torch.rand(1).item()
                virtual_point = alpha * point1 + (1 - alpha) * point2
                virtual_fx = alpha * fx1 + (1 - alpha) * fx2
                
                virtual_x_list.append(virtual_point)
                virtual_fx_list.append(virtual_fx)
        
        if virtual_x_list:
            virtual_x = torch.stack(virtual_x_list)
            virtual_fx = torch.tensor(virtual_fx_list)
            
            # 合并数据
            x_augmented = torch.cat([x_real, virtual_x], dim=0)
            fx_augmented = torch.cat([fx_real, virtual_fx], dim=0)
            
            logging.info(f"✅ 生成了 {len(virtual_x_list)} 个虚拟点")
            logging.info(f"增强后数据: {len(x_augmented)} 个点")
            logging.info(f"虚拟函数值范围: [{virtual_fx.min():.3f}, {virtual_fx.max():.3f}]")
            
            # 测试增强数据的效果
            from bounce.gaussian_process import get_rbf_model
            
            # 原始数据训练的模型
            rbf_original = get_rbf_model(x_real, fx_real)
            
            # 增强数据训练的模型
            rbf_augmented = get_rbf_model(x_augmented, fx_augmented)
            
            # 在测试点上比较
            x_test = torch.rand(5, 8)
            
            mean_orig, std_orig = rbf_original.predict(x_test, return_std=True)
            mean_aug, std_aug = rbf_augmented.predict(x_test, return_std=True)
            
            logging.info(f"\n📈 虚拟点增强效果:")
            logging.info(f"原始模型不确定性: {std_orig.mean():.4f}")
            logging.info(f"增强模型不确定性: {std_aug.mean():.4f}")
            logging.info(f"不确定性变化: {((std_aug.mean() - std_orig.mean()) / std_orig.mean() * 100):.1f}%")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 虚拟点演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logging.info("🚀 RBF代理模型功能演示")
    
    success1 = demo_rbf_vs_gp()
    success2 = demo_virtual_points()
    
    if success1 and success2:
        logging.info("\n🎉 所有演示成功完成！")
        logging.info("✅ RBF模型功能正常工作")
        logging.info("✅ 虚拟点生成概念验证成功")
        logging.info("\n📝 总结:")
        logging.info("1. RBF代理模型已成功实现，可以作为GP模型的替代方案")
        logging.info("2. 虚拟点生成机制已实现，可以在初始样本少时增强训练数据")
        logging.info("3. 多代理模型系统已集成到candidates.py中")
        logging.info("4. 可以通过gin配置选择使用GP或RBF模型")
    else:
        logging.warning("⚠️ 部分演示失败，需要进一步调试")
