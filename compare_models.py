#!/usr/bin/env python3
"""
对比不同代理模型和虚拟点配置的实验脚本
"""

import logging
import time
import gin
import torch
import numpy as np
import pandas as pd
from pathlib import Path

from bounce.bounce import Bounce
from bounce.benchmarks import ShiftedAckley10

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_single_experiment(config_name, config_dict, n_runs=3):
    """
    运行单个配置的实验
    
    Args:
        config_name: 配置名称
        config_dict: 配置字典
        n_runs: 运行次数
        
    Returns:
        results: 实验结果列表
    """
    logging.info(f"\n{'='*60}")
    logging.info(f"🧪 开始实验: {config_name}")
    logging.info(f"{'='*60}")
    
    results = []
    
    for run in range(n_runs):
        logging.info(f"📊 运行 {run + 1}/{n_runs}")
        
        # 清除gin配置
        gin.clear_config()
        
        # 设置基础配置
        base_config = [
            "Bounce.maximum_number_evaluations = 50",
            "create_candidates_discrete.population_size = 50",
            "create_candidates_discrete.n_generations = 10",
        ]
        
        # 添加特定配置
        full_config = base_config + config_dict.get('gin_config', [])
        gin.parse_config(full_config)
        
        # 创建基准测试
        benchmark = ShiftedAckley10()
        
        # 创建优化器
        bounce = Bounce(
            benchmark=benchmark,
            device="cpu",
            number_initial_points=3,
            initial_target_dimensionality=5,
            number_new_bins_on_split=2,
            maximum_number_evaluations=50,
            batch_size=1,
            results_dir="./temp_results",
        )
        
        # 运行优化
        start_time = time.time()
        try:
            bounce.run()
            end_time = time.time()
            
            # 记录结果
            best_value = bounce.fx_tr.min().item()
            n_evals = len(bounce.fx_tr)
            runtime = end_time - start_time
            
            # 获取优化历史
            history = bounce.fx_tr.cpu().numpy()
            cumulative_best = np.minimum.accumulate(history)
            
            result = {
                'config': config_name,
                'run': run + 1,
                'best_value': best_value,
                'n_evaluations': n_evals,
                'runtime': runtime,
                'history': cumulative_best.tolist(),
                'success': True
            }
            
            logging.info(f"✅ 运行 {run + 1} 完成: 最优值 = {best_value:.4f}, 评估次数 = {n_evals}, 时间 = {runtime:.2f}s")
            
        except Exception as e:
            logging.error(f"❌ 运行 {run + 1} 失败: {e}")
            result = {
                'config': config_name,
                'run': run + 1,
                'best_value': float('inf'),
                'n_evaluations': 0,
                'runtime': 0,
                'history': [],
                'success': False,
                'error': str(e)
            }
        
        results.append(result)
    
    return results

def compare_all_configurations():
    """对比所有配置"""
    
    logging.info("🚀 开始代理模型和虚拟点对比实验")
    
    # 定义实验配置
    configurations = {
        "GP_baseline": {
            "description": "传统GP模型（基线）",
            "gin_config": [
                "create_candidates_discrete.use_rbf_model = False",
                "create_candidates_discrete.use_virtual_points = False",
            ]
        },
        "RBF_baseline": {
            "description": "RBF模型（无虚拟点）",
            "gin_config": [
                "create_candidates_discrete.use_rbf_model = True",
                "create_candidates_discrete.use_virtual_points = False",
            ]
        },
        "GP_with_virtual": {
            "description": "GP模型 + 虚拟点增强",
            "gin_config": [
                "create_candidates_discrete.use_rbf_model = False",
                "create_candidates_discrete.use_virtual_points = True",
                "create_candidates_discrete.n_virtual_points = 15",
            ]
        },
        "RBF_with_virtual": {
            "description": "RBF模型 + 虚拟点增强",
            "gin_config": [
                "create_candidates_discrete.use_rbf_model = True",
                "create_candidates_discrete.use_virtual_points = True",
                "create_candidates_discrete.n_virtual_points = 15",
            ]
        }
    }
    
    all_results = []
    
    # 运行所有配置
    for config_name, config_info in configurations.items():
        logging.info(f"\n📝 配置说明: {config_info['description']}")
        results = run_single_experiment(config_name, config_info, n_runs=3)
        all_results.extend(results)
    
    # 分析结果
    analyze_results(all_results, configurations)
    
    return all_results

def analyze_results(results, configurations):
    """分析实验结果"""
    
    logging.info(f"\n{'='*60}")
    logging.info("📈 实验结果分析")
    logging.info(f"{'='*60}")
    
    # 按配置分组
    config_results = {}
    for result in results:
        if result['success']:
            config_name = result['config']
            if config_name not in config_results:
                config_results[config_name] = []
            config_results[config_name].append(result)
    
    # 创建结果表格
    summary_data = []
    
    for config_name, config_info in configurations.items():
        if config_name in config_results:
            config_res = config_results[config_name]
            
            best_values = [r['best_value'] for r in config_res]
            runtimes = [r['runtime'] for r in config_res]
            n_evals = [r['n_evaluations'] for r in config_res]
            
            summary_data.append({
                '配置': config_name,
                '描述': config_info['description'],
                '平均最优值': np.mean(best_values),
                '最优值标准差': np.std(best_values),
                '最佳值': np.min(best_values),
                '平均运行时间(s)': np.mean(runtimes),
                '平均评估次数': np.mean(n_evals),
                '成功率': f"{len(config_res)}/3"
            })
            
            logging.info(f"\n🔍 {config_name} ({config_info['description']}):")
            logging.info(f"  平均最优值: {np.mean(best_values):.4f} ± {np.std(best_values):.4f}")
            logging.info(f"  最佳值: {np.min(best_values):.4f}")
            logging.info(f"  平均运行时间: {np.mean(runtimes):.2f} ± {np.std(runtimes):.2f} 秒")
            logging.info(f"  平均评估次数: {np.mean(n_evals):.1f}")
    
    # 保存详细结果
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv("model_comparison_summary.csv", index=False, encoding='utf-8-sig')
    logging.info(f"\n📁 详细结果已保存到 model_comparison_summary.csv")
    
    # 显示排名
    if summary_data:
        best_config = min(summary_data, key=lambda x: x['平均最优值'])
        logging.info(f"\n🏆 最佳配置: {best_config['配置']} ({best_config['描述']})")
        logging.info(f"   平均最优值: {best_config['平均最优值']:.4f}")
        
        # 对比分析
        logging.info(f"\n📊 关键对比:")
        
        # GP vs RBF 对比
        gp_baseline = next((x for x in summary_data if x['配置'] == 'GP_baseline'), None)
        rbf_baseline = next((x for x in summary_data if x['配置'] == 'RBF_baseline'), None)
        
        if gp_baseline and rbf_baseline:
            improvement = ((gp_baseline['平均最优值'] - rbf_baseline['平均最优值']) / gp_baseline['平均最优值']) * 100
            time_ratio = rbf_baseline['平均运行时间(s)'] / gp_baseline['平均运行时间(s)']
            logging.info(f"  RBF vs GP: 性能改进 {improvement:+.1f}%, 速度比例 {time_ratio:.2f}x")
        
        # 虚拟点效果对比
        rbf_no_virtual = next((x for x in summary_data if x['配置'] == 'RBF_baseline'), None)
        rbf_with_virtual = next((x for x in summary_data if x['配置'] == 'RBF_with_virtual'), None)
        
        if rbf_no_virtual and rbf_with_virtual:
            virtual_improvement = ((rbf_no_virtual['平均最优值'] - rbf_with_virtual['平均最优值']) / rbf_no_virtual['平均最优值']) * 100
            logging.info(f"  虚拟点增强效果: 性能改进 {virtual_improvement:+.1f}%")

def show_usage_guide():
    """显示使用指南"""
    
    logging.info(f"\n{'='*60}")
    logging.info("📖 参数修改指南")
    logging.info(f"{'='*60}")
    
    logging.info("""
🔧 如何修改参数来切换不同配置:

1️⃣ 使用传统GP模型:
   gin.parse_config([
       "create_candidates_discrete.use_rbf_model = False",
       "create_candidates_discrete.use_virtual_points = False",
   ])

2️⃣ 使用RBF模型:
   gin.parse_config([
       "create_candidates_discrete.use_rbf_model = True", 
       "create_candidates_discrete.use_virtual_points = False",
   ])

3️⃣ 使用RBF模型 + 虚拟点增强:
   gin.parse_config([
       "create_candidates_discrete.use_rbf_model = True",
       "create_candidates_discrete.use_virtual_points = True",
       "create_candidates_discrete.n_virtual_points = 20",  # 虚拟点数量
   ])

4️⃣ 调整其他参数:
   gin.parse_config([
       "create_candidates_discrete.population_size = 100",     # 遗传算法种群大小
       "create_candidates_discrete.n_generations = 20",       # 进化代数
       "Bounce.maximum_number_evaluations = 200",             # 最大评估次数
   ])

💡 建议的参数组合:
   - 快速测试: population_size=50, n_generations=10, n_virtual_points=10
   - 标准设置: population_size=100, n_generations=20, n_virtual_points=20  
   - 高精度: population_size=200, n_generations=30, n_virtual_points=30
""")

if __name__ == "__main__":
    # 显示使用指南
    show_usage_guide()
    
    # 运行对比实验
    try:
        results = compare_all_configurations()
        logging.info("\n🎉 所有实验完成!")
        
    except Exception as e:
        logging.error(f"❌ 实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
