import time
import gin
import argparse
import logging
from bounce.util.printing import BColors, BOUNCE_NAME

from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import KFold
import numpy as np
# import matplotlib.pyplot as plt
import torch
from bounce.gaussian_process import fit_mll, get_gp
from bounce.projection import AxUS, Bin
from bounce.trust_region import TrustRegion, update_tr_state
from botorch.acquisition import ExpectedImprovement
from bounce.candidates import (
    create_candidates_continuous, 
    create_candidates_discrete,
)
from bounce.util.data_handling import (
    construct_mixed_point,
    from_1_around_origin,
    join_data,
    sample_binary,
    sample_categorical,
    sample_continuous,
)
from bounce_ga.bounce_ga import <PERSON><PERSON><PERSON>
def evaluate_surrogate_model(
                             bounce,
                             ):
    """
    评估代理模型的性能，包括MAE和RMSE指标。

    参数：
    surrogate_model (GPModel): 代理模型
    X_test (torch.Tensor): 测试集输入
    y_test (torch.Tensor): 测试集输出
    cv_folds (int): 交叉验证折数，默认为5

    返回：
    mae (float): 平均绝对误差
    rmse (float): 均方根误差
    """
    # 用于存储所有批次的预测值和真实值
    all_preds = []
    all_truths = []
    while bounce._n_evals <= bounce.maximum_number_evaluations:
        # 信赖域中的样本
        x = bounce.x_tr

        # 及其函数值（fx）
        fx = bounce.fx_tr

        # normalize data
        mean = torch.mean(fx)
        std = torch.std(fx)
        if std == 0:
            std += 1
        fx_scaled = (fx - mean) / std
        x_scaled = (x + 1) / 2

        model, train_x, train_fx = get_gp(
            axus=bounce.random_embedding,
            x=x_scaled,
            fx=-fx_scaled,  # GP模型使用负值
        )

        # 训练GP 这一步才是真正的训练GP模型
        fit_mll(
            model=model,
            train_x=train_x,
            train_fx=-train_fx,
            max_cholesky_size=bounce.max_cholesky_size,
            use_scipy_lbfgs=True,
        )
        sampler=None
        acquisition_function = ExpectedImprovement(
            model=model, best_f=(-fx_scaled).max().item()
        )
        x_best, fx_best, tr_state = create_candidates_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            model=model,
            axus=bounce.random_embedding,
            trust_region=bounce.trust_region,
            device=bounce.device,
            batch_size=bounce.batch_size,
            acquisition_function=acquisition_function,
            sampler=sampler,
        )
        fx_best = fx_best * std + mean
        preds = fx_best.detach().cpu().numpy().ravel()
        tmp = preds.tolist()
        all_preds.extend(preds.tolist())
        # step 6
        # get the GP hyperparameters as a dictionary
        # 把当前信任区域 tr_state 保存一下（比如保存当前最优长度、维度等）；
        bounce.save_tr_state(tr_state)

        # 拷贝当前找到的最优点和预测值（从 GPU -> CPU）
        minimum_xs = x_best.detach().cpu()
        minimum_fxs = fx_best.detach().cpu()


        # fx_batches 初始化（用于找最小值）
        fx_batches = minimum_fxs

        # 创建空张量来放最终将被评估的高维输入点，形状为 [batch_size, 原始高维输入维度] 的空数组
        cand_batch = torch.empty(
            (bounce.batch_size, bounce.benchmark.representation_dim), dtype=bounce.dtype
        )

        # 准备两个列表收集低维 / 高维的观察点 xs_low_dim：记录我们在低维空间里选中的点；xs_high_dim：记录我们升维之后的点。
        xs_low_dim = list()
        xs_high_dim = list()
        
        #  下面是一个循环：每次从 x_best 中挑出一个最好的点（按 fx），投影升维后加入候选集
        for batch_index in range(bounce.batch_size):
            # Find the row (tr index) and column (batch index) of the minimum
            # ----- 选出当前最优值的下标 ----- fx_batches是一维张量，fx_batches.min()得到那个值，但是where()方法返回的也是一个张量，[0]就是一个标量了
            col = torch.where(fx_batches == fx_batches.min())[0]

            # Find the point that gave the minimum
            # 取出这个点
            x_elect = minimum_xs[col[0]]

            # 若 x_elect 是 1 维，则扩展维度。我们希望它是 (1, target_dim)（二维）（TODO，为什么希望是2维 ）
            if len(x_elect.shape) == 1:
                # avoid transpose warnings
                # 执行 x_elect.unsqueeze(0) 后，张量的内容不变，例如x_elect= [1, 2, 3, 4]，执行之后只是多了一层包裹，变成了 [[1, 2, 3, 4]]
                x_elect = x_elect.unsqueeze(0)

            # Add the point to the lower-dimensional observations
            # 把这个低维点保存起来
            xs_low_dim.append(x_elect)

            # Project the point up to the high dimensional space
            # 投影回原始高维空间（升维），这之后的代码做了两件事：
            # 1、将低维点从 target_dim 投影回 representation_dim（原始空间）   project_up（）
            # 2、把投影后的点从 [-1, 1] 空间映射到原始变量空间                 from_1_around_origin（）
            x_elect_up = from_1_around_origin(
                # 将低维点从 target_dim 投影回 representation_dim
                bounce.random_embedding.project_up(x_elect.T).T,
                lb=bounce.benchmark.lb_vec,
                ub=bounce.benchmark.ub_vec,
            )

            # Add the point to the high-dimensional observations
            # 把高维点保存下来
            xs_high_dim.append(x_elect_up)

            # Add the point to the batch to be evaluated
            # squeeze() 去掉额外维度（比如 (1, 100) 变成 (100,)）；放到 cand_batch 的第 batch_index 行
            cand_batch[batch_index, :] = x_elect_up.squeeze()

            # Set the value of the minimum to infinity so that it is not selected again
            # 把已经选过的点的值设置为 inf，防止重复选择
            fx_batches[col[0]] = torch.inf

        # Sample on the candidate points
        # 正式调用黑盒函数评估 返回每个点的目标值（真实的，不是 GP 预测的了！）
        y_next = bounce.benchmark(cand_batch)
        print(y_next)
        # 将张量移到 CPU 并扁平化为 NumPy 数组

        reals = y_next.detach().cpu().numpy().ravel()

        # 累积到列表中

        all_truths.extend(reals.tolist())

        # 实时打印本批次每个样本的预测 vs. 真实
        batch_idx = bounce._n_evals // bounce.batch_size
        for i, (p, r) in enumerate(zip(tmp, reals)):
            print(f"[Batch {batch_idx} – Sample {i}] 预测 = {p:.6f}, 真实 = {r:.6f}")

        # Calculate the estimated trust region dimensionality
        # 取出当前信赖域的“维度等级”（用于分预算）
        # (TODO)为什么不直接用target_dim? _forecasted_tr_dim 和 target_dim 不一定是相同的！ 为什么调用的方法没加小括号
        #  Bounce 的核心设计之一是：提前为某个维度维持一个“动态预算”系统，作为信赖域迭代分配依据
        tr_dim = bounce._forecasted_tr_dim

        # Number of times this trust region has been selected
        # Remaining budget for this trust region
        # 获取这个维度下还剩多少预算，不能超过总剩余评估次数，也不能为 0（否则计算 log 时会炸）。
        remaining_budget = bounce._all_split_budgets[tr_dim]
        remaining_budget = min(
            remaining_budget, bounce.maximum_number_evaluations - bounce._n_evals
        )
        remaining_budget_update = remaining_budget
        remaining_budget = max(remaining_budget, 1)

        # 拿到当前信赖域对象
        tr = bounce.trust_region

        # 计算信赖域更新因子
        factor = (tr.length_min_discrete / tr.length_discrete_continuous) ** (
                1 / remaining_budget
        )
        factor **= bounce.batch_size
        factor = np.clip(factor, a_min=1e-10, a_max=None)

        # 打印调整信息
        logging.debug(
            f"🔎 Adjusting trust region by factor {factor.item():.3f}. Remaining budget: {remaining_budget}"
        )

        # 更新信赖域的内部状态；作用：对比本轮评估点的最小函数值（fx_next）和历史最优（fx_incumbent）；
        # 如果没有提升，则会收缩信赖域；
        # 如果有提升，可能会扩大信赖域；
        update_tr_state(
            trust_region=bounce.trust_region,
            fx_next=y_next.min(),
            fx_incumbent=bounce.fx_tr.min(),
            adjustment_factor=factor,
            remaining_budget=remaining_budget_update
        )

        # 输出信赖域当前长度信息
        logging.debug(
            f"📏 Trust region has length {tr.length_discrete_continuous:.3f} and minium l {tr.length_min_discrete:.3f}"
        )

        # 更新每个维度的预算以及执行过程中真实函数评估次数
        bounce._all_split_budgets[tr_dim] = (
                bounce._all_split_budgets[tr_dim] - bounce.batch_size
        )


        # 把“新获得的数据”添加到 局部数据集中
        bounce._add_data_to_tr_observations(
            xs_down=torch.vstack(xs_low_dim),
            xs_up=torch.vstack(xs_high_dim),
            fxs=y_next.reshape(-1),
        )

        # 新增：记录当前评估次数和当前最优值
        bounce.eval_history.append([bounce._n_evals, bounce.fx_tr.min().item()])
        
        bounce._n_evals += bounce.batch_size
        # Splitting trust regions that terminated
        # 判断当前信赖域是否“终止”
        if bounce.trust_region.terminated:
            # 当前还没达到最大维度，就准备 split
            if bounce.random_embedding.target_dim < bounce.benchmark.representation_dim:
                # Full dim is not reached yet
                # 进入 Split 模式（增加 target_dim）
                logging.info(f"✂️ Splitting trust region")

                #  调用 random_embedding.split(...) 增加 target_dim
                index_mapping = bounce.random_embedding.split(
                    bounce.number_new_bins_on_split
                )

                # move data to higher-dimensional space
                # 把旧数据转换到新维度
                bounce.x_tr = join_data(bounce.x_tr, index_mapping)
                bounce.x_global = join_data(bounce.x_global, index_mapping)

                # 创建一个新的信赖域对象
                bounce.trust_region = TrustRegion(
                    dimensionality=bounce.random_embedding.target_dim
                )

                if bounce.tr_splits < bounce._n_splits:
                    bounce.tr_splits += 1

                # 更新新的 split 预算
                bounce.split_budget = bounce._split_budget(
                    bounce.initial_target_dimensionality
                    * (bounce.number_new_bins_on_split + 1) ** bounce.tr_splits
                )
            # 当前维度 >= 最大维度，就不能split了，但是预算还没花光，所以再多迭代一次
            else:
                # Full dim is reached
                logging.info(
                    f"🏁 Reached full dimensionality. Restarting with new random samples."
                )
                # 分配预算
                bounce.split_budget = bounce._split_budget(
                    bounce.random_embedding.input_dim
                )
                # Reset the last split budget
                # 当前维度的预算重新设置一下
                bounce._all_split_budgets[bounce._forecasted_tr_dim] = bounce.split_budget

                # empty tr data, does not delete the global data
                # 清空局部数据（但保留全局数据）
                bounce._reset_local_data()

                # reset the trust region
                # 重置信赖域（但不变维度）
                bounce.trust_region.reset()

                bounce.sample_init()
            # 循环结束后，计算整体 MAE 和 RMSE
    mae  = mean_absolute_error(all_truths, all_preds)
    rmse = mean_squared_error(all_truths, all_preds, squared=False)

    # 打印最终评估结果
    print("\n======= Surrogate 模型最终评估 =======")
    print(f"总样本数       : {len(all_truths)}")
    print(f"平均绝对误差   : {mae:.6f}")
    print(f"均方根误差     : {rmse:.6f}")
    print("======================================\n")

if __name__ == "__main__":

    
    then = time.time()
    parser = argparse.ArgumentParser(
        prog=BOUNCE_NAME,
        description="Bounce: Reliable High-Dimensional Bayesian Optimization Algorithm for Combinatorial and Mixed Spaces",
        epilog="For more information, please contact the author.",
    )

    parser.add_argument(
        "--gin-files",
        type=str,
        nargs="+",
        default=["bounce_ga/default.gin"],
        help="Path to the config file",
    )
    parser.add_argument(
        "--gin-bindings",
        type=str,
        nargs="+",
        default=[],
    )

    parser.add_argument(
        "--n-repeat",
        type=int,
        default=14,
        help="实验重复次数"
    )
    args = parser.parse_args()
    gin.parse_config_files_and_bindings(args.gin_files, args.gin_bindings)
    bounce1 = Bounce()
    evaluate_surrogate_model(bounce1)
    gin.clear_config()

