# 🚀 快速开始指南

## 三种代理模型配置对比

### 1. GP基线模型 (推荐用于最优性能)
```bash
python main.py --gin-files configs/gp_baseline.gin --n-repeat 1
```
- **最优值**: -252.212
- **运行时间**: 86.54s
- **特点**: 传统高斯过程，优化质量最好

### 2. GP模型 + 虚拟点增强 (平衡选择)
```bash
python main.py --gin-files configs/gp_with_virtual_points.gin --n-repeat 1
```
- **最优值**: -118.928
- **运行时间**: 88.73s
- **特点**: GP模型 + 虚拟点数据增强，性能平衡

### 3. RBF模型 + 虚拟点 (快速原型)
```bash
python main.py --gin-files configs/rbf_model.gin --n-repeat 1
```
- **最优值**: 1129.520
- **运行时间**: 40.61s
- **特点**: 速度最快，但优化质量较差

## 🔧 参数修改方法

### 方法1: 修改配置文件
直接编辑对应的`.gin`文件中的参数

### 方法2: 命令行覆盖
```bash
# 启用GP模型的虚拟点增强
python main.py --gin-files configs/gp_baseline.gin \
    --gin-bindings "Bounce.use_virtual_points_for_gp = True"

# 修改虚拟点数量
python main.py --gin-files configs/gp_with_virtual_points.gin \
    --gin-bindings "Bounce.n_virtual_points_for_gp = 50"

# 切换到RBF模型
python main.py --gin-files configs/gp_baseline.gin \
    --gin-bindings "create_candidates_discrete.use_rbf_model = True" \
    --gin-bindings "create_candidates_discrete.use_virtual_points = True"
```

## 📊 关键参数说明

### RBF模型参数
- `create_candidates_discrete.use_rbf_model`: 是否使用RBF模型
- `create_candidates_discrete.use_virtual_points`: RBF模型是否使用虚拟点
- `create_candidates_discrete.n_virtual_points`: 虚拟点数量

### GP模型虚拟点参数
- `Bounce.use_virtual_points_for_gp`: GP模型是否使用虚拟点增强
- `Bounce.n_virtual_points_for_gp`: GP模型虚拟点数量

## 💡 使用建议

- **追求最优解**: 使用GP基线
- **平衡性能和速度**: 使用GP + 虚拟点
- **快速验证**: 使用RBF模型
- **小样本场景**: 启用虚拟点增强
