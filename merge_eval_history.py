import pandas as pd
import glob
import pathlib

# 递归查找所有实验的eval_history.csv
result_files = sorted(glob.glob("results/PestControl/*/*/eval_history.csv"), key=lambda x: pathlib.Path(x).stat().st_mtime)
all_histories = []
all_steps = []

for f in result_files:
    df = pd.read_csv(f)
    all_histories.append(df["当前最优值"].values)
    all_steps.append(df["评估次数"].values)

max_len = max(len(h) for h in all_histories)
aligned = []
for h in all_histories:
    if len(h) < max_len:
        h = list(h) + [h[-1]] * (max_len - len(h))
    aligned.append(h)
aligned = pd.DataFrame(aligned).T
aligned.columns = [f"实验{i+1}" for i in range(len(result_files))]
step_col = all_steps[all_histories.index(max(all_histories, key=len))]
aligned.insert(0, "评估次数", step_col)
aligned["均值"] = aligned.iloc[:, 1:].mean(axis=1)
aligned.to_csv("eval_history.csv", index=False, float_format="%.8f", encoding="utf-8-sig")
print(f"合并完成，共{len(result_files)}个实验，结果已保存到eval_history.csv")
