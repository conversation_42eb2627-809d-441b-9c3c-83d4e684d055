#!/usr/bin/env python3
"""
快速对比不同代理模型配置的脚本
"""

import logging
import time
import gin
import torch
import numpy as np
from bounce.benchmarks import ShiftedAckley10
from bounce.bounce import Bounce

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_quick_test(config_name, gin_config, max_evals=20):
    """运行快速测试"""
    
    logging.info(f"\n🧪 测试配置: {config_name}")
    logging.info("="*50)
    
    # 清除gin配置
    gin.clear_config()
    
    # 设置配置
    base_config = [
        f"Bounce.maximum_number_evaluations = {max_evals}",
        "create_candidates_discrete.population_size = 30",
        "create_candidates_discrete.n_generations = 5",
    ]
    
    full_config = base_config + gin_config
    gin.parse_config(full_config)
    
    # 创建基准测试
    benchmark = ShiftedAckley10()
    
    # 创建优化器
    bounce = Bounce(
        benchmark=benchmark,
        device="cpu",
        number_initial_points=3,
        initial_target_dimensionality=5,
        number_new_bins_on_split=2,
        maximum_number_evaluations=max_evals,
        batch_size=1,
        results_dir="./temp_results",
    )
    
    # 运行优化
    start_time = time.time()
    try:
        bounce.run()
        end_time = time.time()
        
        # 记录结果
        best_value = bounce.fx_tr.min().item()
        n_evals = len(bounce.fx_tr)
        runtime = end_time - start_time
        
        logging.info(f"✅ 最优值: {best_value:.4f}")
        logging.info(f"📊 评估次数: {n_evals}")
        logging.info(f"⏱️ 运行时间: {runtime:.2f}s")
        
        return {
            'config': config_name,
            'best_value': best_value,
            'n_evaluations': n_evals,
            'runtime': runtime,
            'success': True
        }
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return {
            'config': config_name,
            'best_value': float('inf'),
            'n_evaluations': 0,
            'runtime': 0,
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    
    logging.info("🚀 快速代理模型对比测试")
    
    # 定义测试配置
    configurations = [
        {
            "name": "GP_baseline",
            "description": "传统GP模型（基线）",
            "config": [
                "create_candidates_discrete.use_rbf_model = False",
                "create_candidates_discrete.use_virtual_points = False",
            ]
        },
        {
            "name": "RBF_baseline", 
            "description": "RBF模型（无虚拟点）",
            "config": [
                "create_candidates_discrete.use_rbf_model = True",
                "create_candidates_discrete.use_virtual_points = False",
            ]
        },
        {
            "name": "RBF_with_virtual",
            "description": "RBF模型 + 虚拟点增强",
            "config": [
                "create_candidates_discrete.use_rbf_model = True",
                "create_candidates_discrete.use_virtual_points = True",
                "create_candidates_discrete.n_virtual_points = 10",
            ]
        }
    ]
    
    results = []
    
    # 运行所有配置
    for config in configurations:
        logging.info(f"\n📝 {config['description']}")
        result = run_quick_test(config['name'], config['config'])
        results.append(result)
    
    # 分析结果
    logging.info(f"\n{'='*60}")
    logging.info("📈 快速对比结果")
    logging.info(f"{'='*60}")
    
    successful_results = [r for r in results if r['success']]
    
    if successful_results:
        # 按性能排序
        successful_results.sort(key=lambda x: x['best_value'])
        
        logging.info(f"\n🏆 性能排名:")
        for i, result in enumerate(successful_results, 1):
            config_info = next(c for c in configurations if c['name'] == result['config'])
            logging.info(f"  {i}. {config_info['description']}")
            logging.info(f"     最优值: {result['best_value']:.4f}")
            logging.info(f"     运行时间: {result['runtime']:.2f}s")
            logging.info(f"     评估次数: {result['n_evaluations']}")
        
        # 对比分析
        if len(successful_results) >= 2:
            best = successful_results[0]
            baseline = next((r for r in successful_results if r['config'] == 'GP_baseline'), None)
            
            if baseline and best['config'] != 'GP_baseline':
                improvement = ((baseline['best_value'] - best['best_value']) / baseline['best_value']) * 100
                speed_ratio = baseline['runtime'] / best['runtime']
                logging.info(f"\n📊 相比GP基线的改进:")
                logging.info(f"  性能提升: {improvement:+.1f}%")
                logging.info(f"  速度比例: {speed_ratio:.2f}x")
        
        # 虚拟点效果分析
        rbf_baseline = next((r for r in successful_results if r['config'] == 'RBF_baseline'), None)
        rbf_virtual = next((r for r in successful_results if r['config'] == 'RBF_with_virtual'), None)
        
        if rbf_baseline and rbf_virtual:
            virtual_improvement = ((rbf_baseline['best_value'] - rbf_virtual['best_value']) / rbf_baseline['best_value']) * 100
            logging.info(f"\n🔮 虚拟点增强效果:")
            logging.info(f"  性能改进: {virtual_improvement:+.1f}%")
    
    # 显示使用指南
    logging.info(f"\n{'='*60}")
    logging.info("📖 如何在main.py中使用不同配置")
    logging.info(f"{'='*60}")
    
    logging.info("""
🔧 修改main.py中的gin配置:

1️⃣ 在main.py的args.gin_bindings中添加配置:

   # 使用RBF模型
   args.gin_bindings = [
       "create_candidates_discrete.use_rbf_model = True",
       "create_candidates_discrete.use_virtual_points = False",
   ]

2️⃣ 或者创建新的gin配置文件 (推荐):

   # 创建 configs/rbf_model.gin
   create_candidates_discrete.use_rbf_model = True
   create_candidates_discrete.use_virtual_points = True
   create_candidates_discrete.n_virtual_points = 20
   
   # 然后运行: python main.py --gin-files configs/rbf_model.gin

3️⃣ 命令行直接指定:

   python main.py --gin-bindings "create_candidates_discrete.use_rbf_model = True" "create_candidates_discrete.use_virtual_points = True"

💡 推荐配置组合:
   - 快速测试: use_rbf_model=True, n_virtual_points=10
   - 标准实验: use_rbf_model=True, use_virtual_points=True, n_virtual_points=20
   - 高精度: use_rbf_model=True, use_virtual_points=True, n_virtual_points=30
""")

if __name__ == "__main__":
    main()
