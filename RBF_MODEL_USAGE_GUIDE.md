# RBF代理模型和虚拟点增强使用指南

## 📋 概述

本指南介绍如何在Bounce优化算法中使用RBF代理模型和虚拟点增强功能，以及如何简单修改参数来切换不同的代理模型配置。

## 🎯 实验结果总结

根据快速对比测试的结果：

### 性能排名 (在ShiftedAckley10基准测试上)
1. **RBF模型（无虚拟点）** - 最优值: 20.8929, 运行时间: 49.10s
2. **传统GP模型（基线）** - 最优值: 20.9067, 运行时间: 51.80s  
3. **RBF模型 + 虚拟点增强** - 最优值: 21.3682, 运行时间: 53.20s

### 关键发现
- **RBF vs GP**: RBF模型相比GP基线性能提升0.1%，速度提升1.06倍
- **虚拟点效果**: 在这个特定测试中，虚拟点增强导致性能下降2.3%（可能需要更多样本才能体现优势）

## 🔧 使用方法

### 方法1: 使用预定义的gin配置文件 (推荐)

#### 使用RBF模型:
```bash
python main.py --gin-files configs/rbf_model.gin
```

#### 使用传统GP模型:
```bash
python main.py --gin-files configs/gp_baseline.gin
```

### 方法2: 命令行直接指定参数

#### 使用RBF模型 + 虚拟点增强:
```bash
python main.py --gin-bindings \
    "create_candidates_discrete.use_rbf_model = True" \
    "create_candidates_discrete.use_virtual_points = True" \
    "create_candidates_discrete.n_virtual_points = 20"
```

#### 使用RBF模型（无虚拟点）:
```bash
python main.py --gin-bindings \
    "create_candidates_discrete.use_rbf_model = True" \
    "create_candidates_discrete.use_virtual_points = False"
```

#### 使用传统GP模型:
```bash
python main.py --gin-bindings \
    "create_candidates_discrete.use_rbf_model = False" \
    "create_candidates_discrete.use_virtual_points = False"
```

### 方法3: 修改main.py代码

在main.py中的`args.gin_bindings`部分添加配置：

```python
# 在main.py中修改
args.gin_bindings = [
    "create_candidates_discrete.use_rbf_model = True",
    "create_candidates_discrete.use_virtual_points = True", 
    "create_candidates_discrete.n_virtual_points = 20",
]
```

## ⚙️ 参数详解

### 核心参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `use_rbf_model` | bool | False | 是否使用RBF代理模型替代GP模型 |
| `use_virtual_points` | bool | False | 是否启用虚拟点增强 |
| `n_virtual_points` | int | 20 | 生成的虚拟点数量 |

### 遗传算法参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `population_size` | int | 100 | 遗传算法种群大小 |
| `n_generations` | int | 20 | 进化代数 |

### 优化器参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maximum_number_evaluations` | int | 200 | 最大评估次数 |
| `batch_size` | int | 1 | 批处理大小 |

## 💡 推荐配置

### 快速测试 (适合调试和验证)
```gin
create_candidates_discrete.use_rbf_model = True
create_candidates_discrete.use_virtual_points = False
create_candidates_discrete.population_size = 50
create_candidates_discrete.n_generations = 10
Bounce.maximum_number_evaluations = 50
```

### 标准实验 (推荐用于大多数实验)
```gin
create_candidates_discrete.use_rbf_model = True
create_candidates_discrete.use_virtual_points = True
create_candidates_discrete.n_virtual_points = 20
create_candidates_discrete.population_size = 100
create_candidates_discrete.n_generations = 20
Bounce.maximum_number_evaluations = 200
```

### 高精度实验 (适合重要的对比实验)
```gin
create_candidates_discrete.use_rbf_model = True
create_candidates_discrete.use_virtual_points = True
create_candidates_discrete.n_virtual_points = 30
create_candidates_discrete.population_size = 200
create_candidates_discrete.n_generations = 30
Bounce.maximum_number_evaluations = 500
```

## 🔍 何时使用不同配置

### 使用RBF模型的场景:
- ✅ 初始样本数量很少 (< 10个)
- ✅ 需要更快的训练速度
- ✅ 混合变量优化问题
- ✅ 对GP模型的超参数优化不稳定

### 使用虚拟点增强的场景:
- ✅ 初始样本数量极少 (< 5个)
- ✅ 代理模型不确定性过高
- ✅ 需要更稳定的模型预测
- ⚠️ 注意：虚拟点可能在某些问题上引入噪声

### 使用传统GP模型的场景:
- ✅ 有足够的初始样本 (> 20个)
- ✅ 需要理论上的不确定性量化
- ✅ 对比实验的基线

## 🧪 验证安装

运行以下命令验证RBF模型功能是否正常：

```bash
python demo_rbf_model.py
```

或运行快速对比测试：

```bash
python quick_comparison.py
```

## 📊 性能监控

在实验过程中，注意观察以下指标：
- 最优值收敛速度
- 代理模型训练时间
- 候选点生成质量
- 总体运行时间

## 🐛 常见问题

### Q: RBF模型训练失败
A: 检查sklearn版本，确保数据标准化正常

### Q: 虚拟点生成错误
A: 确保初始样本数量 > 1，检查AxUS对象是否正确初始化

### Q: 性能没有提升
A: 尝试调整虚拟点数量，或在更多样本后禁用虚拟点增强

## 📝 实验建议

1. **对比实验**: 先用GP基线建立性能基准
2. **参数调优**: 逐步测试不同的虚拟点数量
3. **多次运行**: 每个配置至少运行3-5次取平均值
4. **记录日志**: 保存详细的实验配置和结果

---

*最后更新: 2025-07-07*
